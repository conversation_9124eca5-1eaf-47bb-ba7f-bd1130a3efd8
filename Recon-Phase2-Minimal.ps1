#Requires -Version 5.1
<#
.SYNOPSIS
    Enhanced Phase 2 PowerShell Reconnaissance & Advanced Vulnerability Scanner (Minimal Version)
.DESCRIPTION
    Advanced security testing tool for authorized penetration testing
.PARAMETER Target
    The target URL (e.g., "https://example.com"). Required parameter.
.PARAMETER Intensity
    The scan intensity level (1-3). Default is 2.
.PARAMETER SkipSubdomains
    Skip subdomain enumeration to reduce scan time
.EXAMPLE
    .\Recon-Phase2-Minimal.ps1 -Target "https://example.com"
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$Target,
    
    [ValidateRange(1, 3)]
    [int]$Intensity = 2,
    
    [switch]$SkipSubdomains
)

# Global Variables
$ErrorActionPreference = "Continue"
$ProgressPreference = "SilentlyContinue"

# Ensure Target URL is properly formatted
if (-not $Target.StartsWith("http")) {
    $Target = "https://" + $Target
}

# Create Reports directory
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$ReportsDir = ".\Reports_$Timestamp"
if (-not (Test-Path -Path $ReportsDir)) {
    New-Item -Path $ReportsDir -ItemType Directory -Force | Out-Null
}

$LogFile = Join-Path -Path $ReportsDir -ChildPath "scan_log.txt"
$SummaryFile = Join-Path -Path $ReportsDir -ChildPath "summary.txt"

# Initialize tracking
$StartTime = Get-Date
$FoundVulnerabilities = @()
$TechStack = @()

# Set TLS protocols
try {
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12, [System.Net.SecurityProtocolType]::Tls11, [System.Net.SecurityProtocolType]::Tls
} catch {
    Write-Host "[!] Could not set TLS protocols. Using system defaults." -ForegroundColor Yellow
}

# User Agent
$UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# Banner
$banner = @"
╔═══════════════════════════════════════════════════════════════════════════╗
║   Enhanced PowerShell Reconnaissance & Vulnerability Scanner v2.5         ║
║   Target: $($Target.PadRight(58)) ║
║   Intensity: $Intensity                                                        ║
╚═══════════════════════════════════════════════════════════════════════════╝
"@

Write-Host $banner -ForegroundColor Cyan

# Helper Functions
function Write-Log {
    param(
        [string]$Message,
        [System.ConsoleColor]$Color = [System.ConsoleColor]::Gray,
        [switch]$Vulnerability
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    $formattedMessage = "[$timestamp] $Message"
    
    Write-Host $formattedMessage -ForegroundColor $Color
    Add-Content -Path $LogFile -Value $formattedMessage
    
    if ($Vulnerability) {
        $script:FoundVulnerabilities += $Message
    }
}

function Invoke-SafeWebRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET",
        [int]$TimeoutSec = 15
    )
    
    try {
        $headers = @{
            "User-Agent" = $UserAgent
            "Accept" = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
        }
        
        $response = Invoke-WebRequest -Uri $Uri -Method $Method -Headers $headers -TimeoutSec $TimeoutSec -UseBasicParsing -ErrorAction Stop
        return $response
    }
    catch {
        return [PSCustomObject]@{
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
            Error = $_.Exception.Message
        }
    }
}

function Get-FaviconHash {
    param([string]$TargetUrl)
    
    Write-Log "Analyzing favicon for framework fingerprinting..." -Color Cyan
    
    try {
        $uri = New-Object System.Uri($TargetUrl)
        $faviconUrl = "$($uri.Scheme)://$($uri.Host)/favicon.ico"
        
        $response = Invoke-SafeWebRequest -Uri $faviconUrl -TimeoutSec 10
        
        if ($response.StatusCode -eq 200) {
            Write-Log "Favicon found and accessible" -Color Green
            $script:TechStack += "Favicon: Accessible"
        } else {
            Write-Log "No favicon found or inaccessible" -Color Yellow
        }
    } catch {
        Write-Log "Error analyzing favicon: $($_.Exception.Message)" -Color Red
    }
}

function Test-BasicSecurity {
    param([string]$TargetUrl)
    
    Write-Log "Testing basic security configurations..." -Color Cyan
    
    try {
        $response = Invoke-SafeWebRequest -Uri $TargetUrl
        
        if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 400) {
            $headers = $response.Headers
            
            # Check for basic security headers
            $securityHeaders = @("Content-Security-Policy", "X-Frame-Options", "X-Content-Type-Options", "Strict-Transport-Security")
            $missingHeaders = @()
            
            foreach ($header in $securityHeaders) {
                if (-not $headers.ContainsKey($header)) {
                    $missingHeaders += $header
                }
            }
            
            if ($missingHeaders.Count -gt 0) {
                Write-Log "Missing security headers: $($missingHeaders -join ', ')" -Color Red -Vulnerability
            } else {
                Write-Log "All basic security headers present" -Color Green
            }
            
            # Check for information disclosure
            $infoHeaders = @("Server", "X-Powered-By")
            foreach ($header in $infoHeaders) {
                if ($headers.ContainsKey($header)) {
                    Write-Log "Information disclosure - $header`: $($headers[$header])" -Color Yellow -Vulnerability
                }
            }
        }
    } catch {
        Write-Log "Error testing basic security: $($_.Exception.Message)" -Color Red
    }
}

function Test-CommonEndpoints {
    param([string]$TargetUrl)
    
    Write-Log "Testing common endpoints..." -Color Cyan
    
    $endpoints = @("robots.txt", "sitemap.xml", "admin", "api", "swagger", "graphql")
    
    foreach ($endpoint in $endpoints) {
        try {
            $testUrl = $TargetUrl.TrimEnd('/') + '/' + $endpoint
            $response = Invoke-SafeWebRequest -Uri $testUrl -TimeoutSec 8
            
            if ($response.StatusCode -eq 200) {
                Write-Log "Accessible endpoint found: $endpoint" -Color Green
                
                if ($endpoint -eq "admin") {
                    Write-Log "Admin panel accessible - potential security risk" -Color Red -Vulnerability
                }
            }
        } catch {
            # Continue silently
        }
    }
}

function Save-Report {
    Write-Log "Generating security report..." -Color Cyan
    
    $endTime = Get-Date
    $duration = $endTime - $StartTime
    
    $summary = @"
=== ENHANCED PHASE 2 RECONNAISSANCE REPORT ===
Target: $Target
Scan Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Duration: $($duration.ToString('hh\:mm\:ss'))
Intensity Level: $Intensity

=== EXECUTIVE SUMMARY ===
Total Vulnerabilities Found: $($FoundVulnerabilities.Count)

=== TECHNOLOGY STACK ===
$($TechStack | Select-Object -Unique | ForEach-Object { "- $_" })

=== VULNERABILITIES ===
$($FoundVulnerabilities | ForEach-Object { "- $_" })

=== RECOMMENDATIONS ===
• Implement missing security headers
• Review exposed endpoints
• Regular security assessments

Full detailed logs available in: $LogFile
"@

    $summary | Out-File -FilePath $SummaryFile -Encoding utf8
    Write-Log "Report saved to: $SummaryFile" -Color Green
}

# Main Execution
Write-Log "Starting Enhanced Phase 2 Reconnaissance..." -Color Yellow

try {
    # Phase 1: Favicon Analysis
    Write-Log "=== Phase 1: Favicon Analysis ===" -Color Yellow
    Get-FaviconHash -TargetUrl $Target
    
    # Phase 2: Basic Security Testing
    Write-Log "=== Phase 2: Basic Security Testing ===" -Color Yellow
    Test-BasicSecurity -TargetUrl $Target
    
    # Phase 3: Common Endpoints
    Write-Log "=== Phase 3: Common Endpoints ===" -Color Yellow
    Test-CommonEndpoints -TargetUrl $Target
    
    # Final Phase: Report Generation
    Write-Log "=== Final Phase: Report Generation ===" -Color Yellow
    Save-Report
    
    # Display summary
    Write-Log "=== SCAN COMPLETE ===" -Color Yellow
    Write-Log "Total Vulnerabilities: $($FoundVulnerabilities.Count)" -Color $(if ($FoundVulnerabilities.Count -gt 0) { "Red" } else { "Green" })
    Write-Log "Reports saved to: $ReportsDir" -Color Green
    
} catch {
    Write-Log "Critical error: $($_.Exception.Message)" -Color Red
    exit 1
}

Write-Host "`n=== Enhanced Phase 2 Reconnaissance Complete ===" -ForegroundColor Cyan
